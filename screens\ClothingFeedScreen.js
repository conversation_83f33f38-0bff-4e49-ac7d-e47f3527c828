import React, { useCallback, useState, useEffect, useRef, useMemo } from 'react';
import { SafeAreaView, View, Alert, Platform, Dimensions, Animated as RNAnimated } from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import {
  useSharedValue,
  useAnimatedStyle,
  runOnJS,
  withTiming
} from 'react-native-reanimated';
import { Gesture } from 'react-native-gesture-handler';
import {
  collection,
  query,
  where,
  getDocs,
  addDoc,
  doc,
  updateDoc,
  increment,
  deleteDoc,
  getDoc,
  arrayUnion,
  arrayRemove,
  setDoc
} from 'firebase/firestore';
import { db, auth } from '../firebase.config';
import CollectionSelectionModal from '../components/CollectionSelectionModal';
import FeedHeader from '../components/FeedHeader';
import EnhancedCategoryFilter from '../components/EnhancedCategoryFilter';
import CardStack from '../components/CardStack';
import FeedAnimations from '../components/FeedAnimations';
import SearchModal from '../components/SearchModal';
import { useClothingFeed } from '../hooks/useClothingFeed';
import { trackSwipeGesture, trackCartAddition, startTiming, endTiming } from '../utils/performanceMonitor';
import { styles } from './ClothingFeedScreen.styles';

// Swipe animation constants
const { width: SCREEN_WIDTH, height: SCREEN_HEIGHT } = Dimensions.get('window');
const ROTATION_ANGLE = 12;
const VISIBLE_CARDS = 3;
const CARD_SCALE_DECREMENT = 0.03;
const CARD_POSITION_OFFSET = 15;
const UP_SWIPE_THRESHOLD = -15;
const SWIPE_SENSITIVITY = 0.005;

const ClothingFeedScreen = ({ navigation }) => {
  const insets = useSafeAreaInsets();
  const categoryFilterRef = useRef(null);

  // Local state for wishlist status (overrides the hook-based one)
  const [localIsCurrentItemWishlisted, setLocalIsCurrentItemWishlisted] = useState(false);

  // ===== CONSOLIDATED SWIPE ANIMATION STATE =====

  // Reanimated values for swipe gestures
  const translateX = useSharedValue(0);
  const translateY = useSharedValue(0);
  const rotate = useSharedValue(0);

  // Card animations for stack effect
  const cardAnimations = useRef(
    Array(VISIBLE_CARDS).fill(0).map((_, index) => ({
      scale: useSharedValue(1 - CARD_SCALE_DECREMENT * index),
      translateY: useSharedValue(CARD_POSITION_OFFSET * index),
      opacity: useSharedValue(1),
    }))
  ).current;

  // Traditional React Native Animated values for feedback animations
  const likeAnimationOpacity = useRef(new RNAnimated.Value(0)).current;
  const dislikeAnimationOpacity = useRef(new RNAnimated.Value(0)).current;
  const likeAnimationScale = useRef(new RNAnimated.Value(1)).current;
  const dislikeAnimationScale = useRef(new RNAnimated.Value(1)).current;

  const {    // State
    items,
    loading,
    backgroundLoading,
    error,
    activeCategory,
    noMoreItems,
    currentIndex,
    categories,
    filteredCategories,
    searchQuery,
    searchQueryHeader,
    searchModalVisible,
    showCollectionModal,
    isLoadingMoreCategories,
    currentUserPhotoURL,
    cartItems,
    showCartAnimation,
    isCurrentItemWishlisted,
    isSearchMode,
    searchResults,
    suggestedCategory,

    // Enhanced filtering state
    activeCategoryType,
    activeSpecificCategories,
    activeColor,
    activeGender,

    // Actions
    setActiveCategory,
    setActiveCategoryType,
    setActiveSpecificCategories,
    setActiveColor,
    setActiveGender,
    setCurrentIndex,
    setSearchQuery,
    setSearchQueryHeader,
    setSearchModalVisible,
    setShowCollectionModal,
    fetchClothingItems,
    addToCart,
    toggleWishlist,
    setInteractedItemIds,
    checkWishlistStatus,
    setIsCurrentItemWishlisted,
    searchItems,
    clearSearch,

    // Computed
    currentUserId
  } = useClothingFeed(navigation);

  // ===== CONSOLIDATED SWIPE ANIMATION FUNCTIONS =====

  // Reset card animations
  const resetCardAnimations = useCallback(() => {
    try {
      const consistentTimingConfig = { duration: 500 };

      translateX.value = withTiming(0, consistentTimingConfig);
      translateY.value = withTiming(0, consistentTimingConfig);
      rotate.value = withTiming(0, consistentTimingConfig);

      for (let i = 0; i < VISIBLE_CARDS - 1; i++) {
        cardAnimations[i].scale.value = withTiming(1 - CARD_SCALE_DECREMENT * (i + 1), consistentTimingConfig);
        cardAnimations[i].translateY.value = withTiming(CARD_POSITION_OFFSET * (i + 1), consistentTimingConfig);
        cardAnimations[i].opacity.value = withTiming(1, consistentTimingConfig);
      }
    } catch (error) {
      console.error("[ClothingFeedScreen] Error in resetCardAnimations:", error);
      translateX.value = 0;
      translateY.value = 0;
      rotate.value = 0;
    }
  }, [translateX, translateY, rotate, cardAnimations]);

  // Trigger like animation
  const triggerLikeAnimation = useCallback(() => {
    console.log('[ClothingFeedScreen] Triggering like animation');

    likeAnimationOpacity.setValue(0);
    likeAnimationScale.setValue(1);

    setTimeout(() => {
      const animDuration = Platform.OS === 'android' ? 100 : 50;

      RNAnimated.timing(likeAnimationOpacity, {
        toValue: 1,
        duration: animDuration,
        useNativeDriver: true
      }).start();

      const displayDuration = Platform.OS === 'android' ? 500 : 300;

      const hideTimer = setTimeout(() => {
        RNAnimated.timing(likeAnimationOpacity, {
          toValue: 0,
          duration: animDuration,
          useNativeDriver: true
        }).start(() => {
          console.log('[ClothingFeedScreen] Like animation completed');
        });
      }, displayDuration);

      return () => clearTimeout(hideTimer);
    }, 10);
  }, [likeAnimationOpacity, likeAnimationScale]);

  // Trigger dislike animation
  const triggerDislikeAnimation = useCallback(() => {
    console.log('[ClothingFeedScreen] Triggering dislike animation');

    dislikeAnimationOpacity.setValue(0);
    dislikeAnimationScale.setValue(1);

    setTimeout(() => {
      const animDuration = Platform.OS === 'android' ? 100 : 50;

      RNAnimated.timing(dislikeAnimationOpacity, {
        toValue: 1,
        duration: animDuration,
        useNativeDriver: true
      }).start();

      const displayDuration = Platform.OS === 'android' ? 500 : 300;

      const hideTimer = setTimeout(() => {
        RNAnimated.timing(dislikeAnimationOpacity, {
          toValue: 0,
          duration: animDuration,
          useNativeDriver: true
        }).start(() => {
          console.log('[ClothingFeedScreen] Dislike animation completed');
        });
      }, displayDuration);

      return () => clearTimeout(hideTimer);
    }, 10);
  }, [dislikeAnimationOpacity, dislikeAnimationScale]);

  // ===== OPTIMIZED SWIPE ACTIONS =====

  // Helper function to store interaction
  const storeInteraction = useCallback(async (itemId, interactionType) => {
    try {
      const interactedQuery = query(
        collection(db, 'users', currentUserId, 'interactedItems'),
        where('itemId', '==', itemId)
      );
      const interactedSnapshot = await getDocs(interactedQuery);

      if (interactedSnapshot.empty) {
        await addDoc(collection(db, 'users', currentUserId, 'interactedItems'), {
          itemId,
          interactedAt: new Date(),
          interactionType
        });
        console.log(`[ClothingFeedScreen] Stored interaction: ${interactionType} for item ${itemId}`);
      }
    } catch (error) {
      console.error('[ClothingFeedScreen] Error storing interaction:', error);
      throw error;
    }
  }, [currentUserId]);

  // Helper function to perform like operation
  const performLikeOperation = useCallback(async (itemId) => {
    try {
      const itemRef = doc(db, 'clothingItems', itemId);
      const itemDoc = await getDoc(itemRef);

      if (!itemDoc.exists()) {
        console.log(`[ClothingFeedScreen] Item ${itemId} does not exist, skipping like operation`);
        return;
      }

      const userLikesRef = doc(db, 'users', currentUserId, 'likes', itemId);
      const userDislikesRef = doc(db, 'users', currentUserId, 'dislikes', itemId);

      await Promise.all([
        updateDoc(itemRef, {
          likedBy: arrayUnion(currentUserId),
          dislikedBy: arrayRemove(currentUserId),
          likeCount: increment(1)
        }),
        setDoc(userLikesRef, { itemId, likedAt: new Date() })
      ]);

      try {
        await deleteDoc(userDislikesRef);
      } catch (e) {
        // Ignore if doesn't exist
      }

      console.log(`[ClothingFeedScreen] Like operation completed for item ${itemId}`);
    } catch (error) {
      console.error('[ClothingFeedScreen] Error in like operation:', error);
      throw error;
    }
  }, [currentUserId]);

  // Helper function to perform dislike operation
  const performDislikeOperation = useCallback(async (itemId) => {
    try {
      const itemRef = doc(db, 'clothingItems', itemId);
      const itemDoc = await getDoc(itemRef);

      if (!itemDoc.exists()) {
        console.log(`[ClothingFeedScreen] Item ${itemId} does not exist, skipping dislike operation`);
        return;
      }

      const userDislikesRef = doc(db, 'users', currentUserId, 'dislikes', itemId);
      const userLikesRef = doc(db, 'users', currentUserId, 'likes', itemId);

      await Promise.all([
        updateDoc(itemRef, {
          dislikedBy: arrayUnion(currentUserId),
          likedBy: arrayRemove(currentUserId),
          dislikeCount: increment(1)
        }),
        setDoc(userDislikesRef, { itemId, dislikedAt: new Date() })
      ]);

      try {
        await deleteDoc(userLikesRef);
      } catch (e) {
        // Ignore if doesn't exist
      }

      console.log(`[ClothingFeedScreen] Dislike operation completed for item ${itemId}`);
    } catch (error) {
      console.error('[ClothingFeedScreen] Error in dislike operation:', error);
      throw error;
    }
  }, [currentUserId]);

  // Helper function to update view count
  const updateViewCount = useCallback(async (itemId) => {
    try {
      const itemRef = doc(db, 'clothingItems', itemId);
      await updateDoc(itemRef, {
        viewCount: increment(1)
      });
      console.log(`[ClothingFeedScreen] View count updated for item ${itemId}`);
    } catch (error) {
      console.error('[ClothingFeedScreen] Error updating view count:', error);
      throw error;
    }
  }, []);

  // Background operations for different swipe types
  const performCartBackgroundOperations = useCallback((swipedItem) => {
    return Promise.allSettled([
      storeInteraction(swipedItem.id, 'cart'),
      performLikeOperation(swipedItem.id)
    ]).then((results) => {
      results.forEach((result, index) => {
        if (result.status === 'rejected') {
          console.error(`[ClothingFeedScreen] Cart background operation ${index + 1} failed:`, result.reason);
        }
      });
      return results;
    });
  }, [currentUserId, storeInteraction, performLikeOperation]);

  const performLikeBackgroundOperations = useCallback((swipedItem) => {
    Promise.allSettled([
      storeInteraction(swipedItem.id, 'like'),
      performLikeOperation(swipedItem.id),
      updateViewCount(swipedItem.id)
    ]).then((results) => {
      results.forEach((result, index) => {
        if (result.status === 'rejected') {
          console.error(`[ClothingFeedScreen] Like background operation ${index + 1} failed:`, result.reason);
        }
      });
    });
  }, [currentUserId, storeInteraction, performLikeOperation, updateViewCount]);

  const performDislikeBackgroundOperations = useCallback((swipedItem) => {
    Promise.allSettled([
      storeInteraction(swipedItem.id, 'dislike'),
      performDislikeOperation(swipedItem.id),
      updateViewCount(swipedItem.id)
    ]).then((results) => {
      results.forEach((result, index) => {
        if (result.status === 'rejected') {
          console.error(`[ClothingFeedScreen] Dislike background operation ${index + 1} failed:`, result.reason);
        }
      });
    });
  }, [currentUserId, storeInteraction, performDislikeOperation, updateViewCount]);

  // Handle different swipe actions
  const handleCartSwipe = useCallback((swipedItem) => {
    console.log(`[ClothingFeedScreen] Processing cart swipe for item ${swipedItem.id}`);

    const cartTracker = trackCartAddition(swipedItem.id);
    startTiming(`cart_animation_trigger_${swipedItem.id}`);

    if (addToCart) {
      addToCart(swipedItem);
    }

    endTiming(`cart_animation_trigger_${swipedItem.id}`);
    cartTracker?.markAnimationTriggered();

    performCartBackgroundOperations(swipedItem).then(() => {
      cartTracker?.markDatabaseComplete();
    });
  }, [currentUserId, addToCart, performCartBackgroundOperations]);

  const handleLikeSwipe = useCallback((swipedItem) => {
    console.log(`[ClothingFeedScreen] Processing like swipe for item ${swipedItem.id}`);
    performLikeBackgroundOperations(swipedItem);
  }, [performLikeBackgroundOperations]);

  const handleDislikeSwipe = useCallback((swipedItem) => {
    console.log(`[ClothingFeedScreen] Processing dislike swipe for item ${swipedItem.id}`);
    performDislikeBackgroundOperations(swipedItem);
  }, [performDislikeBackgroundOperations]);

  // Main swipe completion processor
  const processSwipeCompletion = useCallback(async (direction, swipedItem) => {
    const swipeTracker = trackSwipeGesture(direction, swipedItem?.id);

    try {
      if (!swipedItem) {
        console.log("[ClothingFeedScreen] No item to process for swipe completion");
        return;
      }

      if (!currentUserId) {
        console.log("[ClothingFeedScreen] No authenticated user, skipping Firestore operations");
        setInteractedItemIds(prevIds => new Set(prevIds).add(swipedItem.id));
        swipeTracker?.endTracking();
        return;
      }

      console.log(`[ClothingFeedScreen] Processing ${direction} swipe for item ${swipedItem.id}`);

      startTiming(`local_state_update_${swipedItem.id}`);
      setInteractedItemIds(prevIds => new Set(prevIds).add(swipedItem.id));
      endTiming(`local_state_update_${swipedItem.id}`);

      if (direction === 'up') {
        handleCartSwipe(swipedItem);
      } else if (direction === 'right') {
        startTiming(`like_animation_trigger_${swipedItem.id}`);
        triggerLikeAnimation();
        endTiming(`like_animation_trigger_${swipedItem.id}`);
        handleLikeSwipe(swipedItem);
      } else if (direction === 'left') {
        startTiming(`dislike_animation_trigger_${swipedItem.id}`);
        triggerDislikeAnimation();
        endTiming(`dislike_animation_trigger_${swipedItem.id}`);
        handleDislikeSwipe(swipedItem);
      }

      swipeTracker?.endTracking();

    } catch (error) {
      console.error('[ClothingFeedScreen] Error in processSwipeCompletion:', error);
      swipeTracker?.endTracking();
    }
  }, [currentUserId, setInteractedItemIds, triggerLikeAnimation, triggerDislikeAnimation, handleCartSwipe, handleLikeSwipe, handleDislikeSwipe]);

  // Process swipe completion with animation reset
  const processSwipeCompletionWithReset = useCallback((direction, swipedItem) => {
    if (processSwipeCompletion) {
      processSwipeCompletion(direction, swipedItem);
    }

    // Reset animations for the next card after a short delay
    setTimeout(() => {
      resetCardAnimations();
    }, 50);
  }, [processSwipeCompletion, resetCardAnimations]);

  // ===== GESTURE HANDLING =====

  // Create pan gesture
  const panGesture = useMemo(() => Gesture.Pan()
    .onStart(() => {
      'worklet';
      const startX = translateX.value;
      const startY = translateY.value;

      translateX.value = startX;
      translateY.value = startY;

      if (Platform.OS === 'android') {
        rotate.value = 0;
      }
    })
    .enabled(currentIndex < items.length)
    .minDistance(Platform.OS === 'android' ? 5 : 0)
    .onUpdate((event) => {
      'worklet';
      const { translationX, translationY } = event;

      // Determine primary direction
      let direction;
      const directionThreshold = Platform.OS === 'android' ? 1.2 : 1.0;

      if (Math.abs(translationX) > Math.abs(translationY) * directionThreshold) {
        direction = 'horizontal';
      } else if (translationY < 0) {
        direction = 'up';
      } else {
        direction = 'down';
      }

      // Handle movement based on direction
      if (direction === 'horizontal') {
        translateX.value = translationX;
        translateY.value = 0;

        // Calculate rotation manually
        const rotationProgress = Math.max(-1, Math.min(1, translationX / (SCREEN_WIDTH / 2)));
        rotate.value = rotationProgress * ROTATION_ANGLE;
      } else if (direction === 'up') {
        translateY.value = Math.min(0, translationY);
        translateX.value = 0;
      } else {
        translateY.value = 0;
        translateX.value = 0;
        return;
      }

      // Calculate animation progress for cards beneath
      let swipeProgress;
      if (direction === 'horizontal') {
        swipeProgress = Math.min(Math.abs(translationX) / (SCREEN_WIDTH * 0.5), 1);
      } else if (direction === 'up') {
        swipeProgress = Math.min(Math.abs(translationY) / 100, 1);
      } else {
        swipeProgress = 0;
      }

      // Animate cards beneath with throttling
      if (swipeProgress > 0.01) {
        for (let i = 0; i < VISIBLE_CARDS - 1; i++) {
          const scaleFrom = 1 - CARD_SCALE_DECREMENT * (i + 1);
          const scaleTo = 1 - CARD_SCALE_DECREMENT * i;
          const translateFrom = CARD_POSITION_OFFSET * (i + 1);
          const translateTo = CARD_POSITION_OFFSET * i;

          cardAnimations[i].scale.value = scaleFrom + (scaleTo - scaleFrom) * swipeProgress;
          cardAnimations[i].translateY.value = translateFrom + (translateTo - translateFrom) * swipeProgress;
          cardAnimations[i].opacity.value = 1;
        }
      }
    })
    .onEnd((event) => {
      'worklet';
      try {
        if (currentIndex < 0 || currentIndex >= items.length) {
          console.warn(`[ClothingFeedScreen] Invalid currentIndex (${currentIndex}) in onEnd. Resetting.`);
          runOnJS(resetCardAnimations)();
          return;
        }

        const { translationX, translationY } = event;
        const swipedItem = items[currentIndex];

        if (!swipedItem) {
          console.warn(`[ClothingFeedScreen] swipedItem is null/undefined in onEnd for index ${currentIndex}. Resetting.`);
          runOnJS(resetCardAnimations)();
          return;
        }

        // Determine primary direction
        let direction;
        if (Math.abs(translationX) > Math.abs(translationY)) {
          direction = 'horizontal';
        } else if (translationY < 0) {
          direction = 'up';
        } else {
          direction = 'down';
        }

        // Handle upward swipe
        if (direction === 'up' && translationY < UP_SWIPE_THRESHOLD) {
          console.log('[ClothingFeedScreen] Upward swipe detected');

          const upSwipeDuration = Platform.OS === 'android' ? 50 : 50;
          const targetY = -SCREEN_HEIGHT * 1.5;

          translateY.value = withTiming(targetY, { duration: upSwipeDuration }, (isFinished) => {
            if (isFinished) {
              runOnJS(processSwipeCompletionWithReset)('up', swipedItem);
            } else {
              runOnJS(resetCardAnimations)();
            }
          });

          translateX.value = withTiming(0, { duration: Platform.OS === 'android' ? upSwipeDuration / 2 : upSwipeDuration });

          if (Platform.OS === 'android') {
            rotate.value = withTiming(0, { duration: upSwipeDuration / 2 });
          }

          return;
        }

        // Handle horizontal swipe
        if (direction === 'horizontal') {
          const sensitiveThreshold = SCREEN_WIDTH * SWIPE_SENSITIVITY;
          if (Math.abs(translationX) > sensitiveThreshold) {
            const swipeDirection = translationX > 0 ? 'right' : 'left';
            const targetX = (swipeDirection === 'right' ? 1 : -1) * SCREEN_WIDTH * 1.5;
            const swipeDuration = Platform.OS === 'android' ? 50 : 50;
            const rotationDuration = Platform.OS === 'android' ? 50 : 50;
            const verticalDuration = Platform.OS === 'android' ? 50 : 50;

            translateX.value = withTiming(targetX, { duration: swipeDuration }, (isFinished) => {
              if (isFinished) {
                runOnJS(processSwipeCompletionWithReset)(swipeDirection, swipedItem);
              } else {
                runOnJS(resetCardAnimations)();
              }
            });

            rotate.value = withTiming((swipeDirection === 'right' ? 1 : -1) * 30, { duration: rotationDuration });
            translateY.value = withTiming(0, { duration: verticalDuration });
            return;
          }
        }

        // No significant swipe detected
        console.log('[ClothingFeedScreen] No significant swipe detected. Resetting.');
        runOnJS(resetCardAnimations)();

      } catch (err) {
        console.error("[ClothingFeedScreen] Error in swipe onEnd:", err);
        runOnJS(resetCardAnimations)();
      }
    }), [currentIndex, items.length, processSwipeCompletionWithReset, resetCardAnimations]);

  // Animated style for top card
  const topCardStyle = useAnimatedStyle(() => ({
    transform: [
      { translateX: translateX.value },
      { translateY: translateY.value },
      { rotate: `${rotate.value}deg` },
    ],
  }));

  const handleSelectCollection = async (selectedCollection, itemId, itemData) => {
    console.log(`[ClothingFeed] handleSelectCollection called for item ${itemId} in collection ${selectedCollection.name}`);

    // The CollectionSelectionModal has already handled the database operations
    // We just need to update the local UI state
    setShowCollectionModal(false);

    // Directly update the local wishlist status for immediate UI feedback
    const currentItem = items[currentIndex];
    if (currentItem && currentItem.id === itemId) {
      console.log(`[ClothingFeed] Setting local wishlist status to true for current item ${itemId}`);
      setLocalIsCurrentItemWishlisted(true);
    }

    console.log(`[ClothingFeed] UI updated for item ${itemId} added to collection ${selectedCollection.name}`);
  };

  // Local function to check wishlist status
  const checkLocalWishlistStatus = useCallback(async (itemIdToCheck) => {
    if (!currentUserId || !itemIdToCheck) {
      setLocalIsCurrentItemWishlisted(false);
      return;
    }
    try {
      const wishlistQuery = query(
        collection(db, 'users', currentUserId, 'wishlist'),
        where('itemId', '==', itemIdToCheck)
      );
      const querySnapshot = await getDocs(wishlistQuery);
      setLocalIsCurrentItemWishlisted(!querySnapshot.empty);
      console.log(`[ClothingFeed] Local wishlist status for item ${itemIdToCheck}: ${!querySnapshot.empty}`);
    } catch (error) {
      console.error('[ClothingFeed] Error checking local wishlist status:', error);
      setLocalIsCurrentItemWishlisted(false);
    }
  }, [currentUserId]);

  // Monitor current item changes and update wishlist status
  useEffect(() => {
    if (items.length > 0 && currentIndex < items.length) {
      const currentItem = items[currentIndex];
      if (currentItem) {
        checkLocalWishlistStatus(currentItem.id);
      }
    } else {
      setLocalIsCurrentItemWishlisted(false);
    }
  }, [currentIndex, items, checkLocalWishlistStatus]);

  // Handle suggested category auto-scrolling and selection
  useEffect(() => {
    if (suggestedCategory && suggestedCategory !== activeCategory) {
      console.log(`[ClothingFeed] Auto-selecting suggested category: ${suggestedCategory}`);

      // Set the active category
      setActiveCategory(suggestedCategory);

      // Scroll to the category in the filter
      if (categoryFilterRef.current) {
        categoryFilterRef.current.scrollToCategory(suggestedCategory);
      }
    }
  }, [suggestedCategory, activeCategory, setActiveCategory]);
  const handleCategorySelect = (category) => {
    setActiveCategory(category);
  };

  const handleSearchSubmit = async (query) => {
    console.log(`[ClothingFeed] Search submitted: "${query}"`);
    await searchItems(query);
  };  const handleClearSearch = () => {
    console.log('[ClothingFeed] Clearing search');
    clearSearch();
    fetchClothingItems(true);
  };

  const handleItemPress = (item) => {
    navigation.navigate('ItemDetails', { itemId: item.id });
  };

  // Override the toggleWishlist function to show collection modal for adding items
  const handleWishlistToggle = async () => {
    if (!currentUserId) {
      console.log("[ClothingFeed] User not logged in, cannot wishlist.");
      return;
    }

    const currentItem = items[currentIndex];
    if (!currentItem) {
      console.log("[ClothingFeed] No current item to wishlist");
      return;
    }

    console.log(`[ClothingFeed] Bookmark button pressed for item ${currentItem.id}, isWishlisted: ${localIsCurrentItemWishlisted}`);

    if (localIsCurrentItemWishlisted) {
      // If already wishlisted, remove from all collections
      console.log(`[ClothingFeed] Removing item ${currentItem.id} from all collections`);
      await handleRemoveFromAllCollections(currentItem.id);
    } else {
      // If not wishlisted, show collection selection modal
      console.log(`[ClothingFeed] Showing collection modal for item ${currentItem.id}`);
      setShowCollectionModal(true);
    }
  };

  // Function to remove item from all collections
  const handleRemoveFromAllCollections = async (itemId) => {
    try {
      const userWishlistRef = collection(db, 'users', currentUserId, 'wishlist');
      const wishlistQuery = query(userWishlistRef, where('itemId', '==', itemId));
      const querySnapshot = await getDocs(wishlistQuery);

      if (!querySnapshot.empty) {
        // Remove from all collections and update counts
        await Promise.all(querySnapshot.docs.map(async (wishlistDoc) => {
          const collectionId = wishlistDoc.data().collectionId;

          // Delete the wishlist document
          await deleteDoc(doc(db, 'users', currentUserId, 'wishlist', wishlistDoc.id));
          console.log(`[ClothingFeed] Removed item ${itemId} from collection ${collectionId}`);

          // Update collection item count
          if (collectionId) {
            const collectionRef = doc(db, 'users', currentUserId, 'collections', collectionId);
            try {
              // Get the current collection to check if this was the last item
              const collectionDoc = await getDoc(collectionRef);

              if (collectionDoc.exists()) {
                const currentItemCount = collectionDoc.data().itemCount || 0;

                // If this was the last item, clear the latestItemImageUrl
                if (currentItemCount <= 1) {
                  await updateDoc(collectionRef, {
                    itemCount: 0,
                    latestItemImageUrl: null
                  });
                } else {
                  // Otherwise just decrement the count
                  await updateDoc(collectionRef, {
                    itemCount: increment(-1)
                  });

                  // If this was the item used as the collection thumbnail, update it
                  const currentItemData = items.find(item => item.id === itemId);
                  if (collectionDoc.data().latestItemImageUrl === currentItemData?.imageUrl) {
                    // We need to find another item in this collection to use as thumbnail
                    const otherItemsQuery = query(
                      collection(db, 'users', currentUserId, 'wishlist'),
                      where('collectionId', '==', collectionId),
                      where('itemId', '!=', itemId)
                    );
                    const otherItemsSnapshot = await getDocs(otherItemsQuery);

                    if (!otherItemsSnapshot.empty) {
                      // Get the first other item's details to use its image
                      const firstOtherItem = otherItemsSnapshot.docs[0].data();
                      const otherItemRef = doc(db, 'clothingItems', firstOtherItem.itemId);
                      const otherItemDoc = await getDoc(otherItemRef);

                      if (otherItemDoc.exists()) {
                        await updateDoc(collectionRef, {
                          latestItemImageUrl: otherItemDoc.data().imageUrl
                        });
                      }
                    }
                  }
                }
              }

              console.log(`[ClothingFeed] Updated collection ${collectionId} after item removal`);
            } catch (e) {
              console.error(`[ClothingFeed] Error updating collection ${collectionId}:`, e);
            }
          }
        }));

        // Decrement saveCount on the clothingItem
        const itemRef = doc(db, 'clothingItems', itemId);
        try {
          await updateDoc(itemRef, { saveCount: increment(-1) });
          console.log(`[ClothingFeed] Decremented saveCount for item ${itemId}`);
        } catch (e) {
          console.error(`[ClothingFeed] Error decrementing saveCount for item ${itemId}:`, e);
        }

        // Directly update the local wishlist status for immediate UI feedback
        const currentItem = items[currentIndex];
        if (currentItem && currentItem.id === itemId) {
          console.log(`[ClothingFeed] Directly setting local wishlist status to false for current item ${itemId}`);
          setLocalIsCurrentItemWishlisted(false);
        }

        console.log(`[ClothingFeed] Item ${itemId} removed from all collections`);
      }
    } catch (error) {
      console.error('[ClothingFeed] Error removing from collections:', error);
      Alert.alert('Error', 'Failed to remove from collections');
    }
  };

  const handleEndReached = () => {
    if (!noMoreItems && !backgroundLoading) {
      fetchClothingItems(false, true);
    }
  };  return (
    <SafeAreaView style={[styles.safeArea, { paddingTop: insets.top }]}>
      <View style={styles.container}>
        {/* Header */}
        <FeedHeader
          currentUserPhotoURL={currentUserPhotoURL}
          currentUserId={currentUserId}
          searchQueryHeader={searchQueryHeader}
          setSearchQueryHeader={setSearchQueryHeader}
          setSearchModalVisible={setSearchModalVisible}
          cartItems={cartItems}
          navigation={navigation}
          onClearSearch={handleClearSearch}
          isSearchMode={isSearchMode}
          searchResultsCount={items.length}
        />

        {/* Enhanced Category Filter */}
        <EnhancedCategoryFilter
          activeCategoryType={activeCategoryType}
          activeSpecificCategories={activeSpecificCategories}
          activeColor={activeColor}
          activeGender={activeGender}          onCategoryTypeSelect={(categoryType) => {
            setActiveCategoryType(categoryType);
          }}
          onSpecificCategoriesChange={(categories) => {
            setActiveSpecificCategories(categories);
          }}onColorSelect={(color) => {
            setActiveColor(color);
          }}
          onGenderSelect={(gender) => {
            setActiveGender(gender);
          }}
          isLoadingMoreCategories={isLoadingMoreCategories}
          onEndReached={handleEndReached}
          autoScrollToCategory={suggestedCategory}
        />

        {/* Main Feed Area */}
        <View style={styles.feedArea}>
          <CardStack
            items={items}
            loading={loading}
            error={error}
            noMoreItems={noMoreItems}
            currentIndex={currentIndex}
            isCurrentItemWishlisted={localIsCurrentItemWishlisted}
            currentUserId={currentUserId}
            onItemPress={handleItemPress}
            onWishlistToggle={handleWishlistToggle}
            onRefresh={() => fetchClothingItems(true)}
            activeCategory={activeCategory}
            setActiveCategory={setActiveCategory}
            // Pass consolidated animation props
            panGesture={panGesture}
            topCardStyle={topCardStyle}
          />
        </View>

        {/* Animations */}
        <FeedAnimations
          likeAnimationOpacity={likeAnimationOpacity}
          dislikeAnimationOpacity={dislikeAnimationOpacity}
          likeAnimationScale={likeAnimationScale}
          dislikeAnimationScale={dislikeAnimationScale}
          showCartAnimation={showCartAnimation}
        />

        {/* Search Modal */}
        <SearchModal
          visible={searchModalVisible}
          onClose={() => setSearchModalVisible(false)}
          searchQuery={searchQuery}
          setSearchQuery={setSearchQuery}
          categories={filteredCategories}
          onCategorySelect={handleCategorySelect}
          onSearchSubmit={handleSearchSubmit}
        />

        {/* Collection Selection Modal */}        <CollectionSelectionModal
          visible={showCollectionModal}
          onClose={() => setShowCollectionModal(false)}
          onSelectCollection={handleSelectCollection}
          itemId={items[currentIndex]?.id}          itemData={items[currentIndex]}
        />
      </View>
    </SafeAreaView>
  );
};

export default ClothingFeedScreen;
